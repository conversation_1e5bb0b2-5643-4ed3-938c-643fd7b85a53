<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useCollectionsStore } from '~/stores/collections'
import { useStreamingChat } from '~/composables/useStreamingChat'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  isStreaming?: boolean
  queryId?: string
  retrievedDocs?: any[]
}

const loading = ref(false)
const messages = ref<Message[]>([])
const messagesContainer = ref<HTMLElement | null>(null)
const showGoalModal = ref(false)
const hasCheckedSession = ref(false)
const streamingMessageId = ref<string | null>(null)

const appStore = useAppStore()
const collectionsStore = useCollectionsStore()
const router = useRouter()
const { sendStreamingMessage, abortStream } = useStreamingChat()
// Auto-hide sidebar on small screens
const shouldAutoHideSidebar = ref(false)

function checkScreenSize() {
  if (typeof window !== 'undefined') {
    shouldAutoHideSidebar.value = window.innerWidth < 1200

    if (shouldAutoHideSidebar.value && appStore.isSidebarVisible) {
      appStore.isSidebarVisible = false
    }
  }
}

onMounted(async () => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)

  // Listen for sidebar events
  window.addEventListener('show-goal-modal', handleShowGoalModal)
  window.addEventListener('show-create-folder-modal', handleShowCreateFolderModal)

  // Check if we have a selected collection
  if (!collectionsStore.currentCollection) {
    // Try to restore from sessionStorage
    const savedCollection = sessionStorage.getItem('selectedCollection')
    if (savedCollection) {
      try {
        const collection = JSON.parse(savedCollection)
        collectionsStore.selectCollection(collection)
      } catch (error) {
        console.error('Error parsing saved collection:', error)
        // Redirect to landing if no valid collection
        router.push('/')
        return
      }
    } else {
      // No collection selected, redirect to landing
      router.push('/')
      return
    }
  }

  // Check if we need to show the goal modal
  await checkSessionStatus()
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', checkScreenSize)
    window.removeEventListener('show-goal-modal', handleShowGoalModal)
    window.removeEventListener('show-create-folder-modal', handleShowCreateFolderModal)
  }
})

async function checkSessionStatus() {
  // Check if there's an active session
  if (!collectionsStore.currentSession) {
    // Check if there are any existing sessions
    if (collectionsStore.unassignedSessions.length > 0
      || collectionsStore.favoriteSessions.length > 0
      || Object.keys(collectionsStore.folderSessions).length > 0) {
      // Sessions exist, let user select from sidebar
      // For now, we can auto-select the most recent unassigned session
      const recentSession = collectionsStore.unassignedSessions[0]
      if (recentSession) {
        await collectionsStore.selectSession(recentSession.id)
      } else {
        // Show goal modal to create new session
        showGoalModal.value = true
      }
    } else {
      // No sessions exist, show goal modal
      showGoalModal.value = true
    }
  }

  hasCheckedSession.value = true
}

async function handleSessionCreated(sessionId: string) {
  // Session was created, it should already be selected in the store
  showGoalModal.value = false

  // Load the session queries if any exist
  if (collectionsStore.currentSession) {
    messages.value = collectionsStore.currentSessionQueries.map(q => ([
      { id: q.id + '-q', role: 'user' as const, content: q.content },
      { id: q.id + '-a', role: 'assistant' as const, content: q.answer }
    ])).flat()
  }
}

function handleShowGoalModal() {
  showGoalModal.value = true
}

function handleShowCreateFolderModal() {
  // For now, we'll show the goal modal with focus on creating a new folder
  showGoalModal.value = true
}

async function sendMessage(messageText: string) {
  if (!messageText.trim() || loading.value) return

  // Check if we have a session and collection
  if (!collectionsStore.currentSession) {
    showGoalModal.value = true
    return
  }

  if (!collectionsStore.currentCollection) {
    console.error('No collection selected')
    return
  }

  // Add user message to chat
  const userMessage: Message = {
    id: Date.now().toString(),
    role: 'user',
    content: messageText
  }
  messages.value.push(userMessage)

  // Create assistant message placeholder
  const assistantMessageId = (Date.now() + 1).toString()
  const assistantMessage: Message = {
    id: assistantMessageId,
    role: 'assistant',
    content: '',
    isStreaming: true
  }
  messages.value.push(assistantMessage)
  streamingMessageId.value = assistantMessageId
  loading.value = true

  // Scroll to bottom
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }

  // Send streaming request
  await sendStreamingMessage({
    collectionId: collectionsStore.currentCollection.id,
    sessionId: collectionsStore.currentSession.id,
    content: messageText,
    customPrompt: collectionsStore.currentCollection.custom_config?.system_prompt,
    topK: 10,
    searchMethod: collectionsStore.currentCollection.custom_config?.method,
    markdownSupport: false, // We'll handle markdown on frontend
    onChunk: (chunk) => {
      // Update the streaming message
      const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
      if (messageIndex !== -1 && chunk.content) {
        messages.value[messageIndex].content += chunk.content

        // Update metadata if available
        if (chunk.query_id) {
          messages.value[messageIndex].queryId = chunk.query_id
        }
        if (chunk.retrieved_docs) {
          messages.value[messageIndex].retrievedDocs = chunk.retrieved_docs
        }

        // Auto-scroll during streaming
        if (messagesContainer.value) {
          const isAtBottom = messagesContainer.value.scrollHeight - messagesContainer.value.scrollTop
            <= messagesContainer.value.clientHeight + 100
          if (isAtBottom) {
            messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
          }
        }
      }
    },
    onComplete: async (fullContent) => {
      // Mark streaming as complete
      const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
      if (messageIndex !== -1) {
        messages.value[messageIndex].isStreaming = false
      }
      streamingMessageId.value = null
      loading.value = false

      // Save to store if needed
      if (messages.value[messageIndex]?.queryId) {
        // Refresh sessions to get updated queries
        if (collectionsStore.currentCollection) {
          await collectionsStore.fetchSessions(collectionsStore.currentCollection.id)
        }
      }
    },
    onError: (error) => {
      console.error('Streaming error:', error)

      // Update error message
      const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
      if (messageIndex !== -1) {
        messages.value[messageIndex].content = 'Sorry, an error occurred while processing your request. Please try again.'
        messages.value[messageIndex].isStreaming = false
      }
      streamingMessageId.value = null
      loading.value = false
    }
  })
}

// Add function to stop streaming if needed
function stopStreaming() {
  if (streamingMessageId.value) {
    abortStream()
    const messageIndex = messages.value.findIndex(m => m.id === streamingMessageId.value)
    if (messageIndex !== -1) {
      messages.value[messageIndex].isStreaming = false
    }
    streamingMessageId.value = null
    loading.value = false
  }
}

definePageMeta({
  layout: 'general'
})
</script>

<template>
  <div class="chat-page">
    <div class="mesh-box" />

    <!-- Header -->
    <div class="chat-header p-2 w-full flex justify-between items-center" :class="{ 'sidebar-open': appStore.isSidebarVisible }">
      <img src="/images/fbeta.png" alt="fbeta" class="md:h-10 mb-8 ml-[16%]">
      <div class="mr-4">
        <UserMenu collapsed />
      </div>
    </div>

    <ChatSidebar />

    <!-- Main Chat Area -->
    <div class="chat-main" :class="{ 'sidebar-open': appStore.isSidebarVisible }">
      <div ref="messagesContainer" class="chat-messages">
        <div v-if="messages.length === 0" class="welcome-container">
          <ChatBubble
            :message="'Willkommen beim DealMasterPro!\n'
              +'\n'
              +'Der DealMasterPro soll Dir helfen, die Einkaufsverhandlungen systematisch vorzubereiten, Expertenwissen zu integrieren und Dir verschiedene Varianten im Verhalten aufzuzeigen.\n'
              +'\n'
              +'Nach der Eingabe des Namens der Lieferanten-Firma und Anlass (z.B. Jahresgespräch, Konditionenverhandlung etc.) der Verhandlung ist es Deine Aufgabe, den Coach mit den für die Verhandlung relevanten Informationen zu versorgen.\n'
              +'\n'
              +'Du kannst diese Informationen entweder sukzessive als Prompting eingeben oder sie auf einem File sammeln und hochladen.\n'
              +'\n'
              +'Die wesentlichen Informationen am Start sind\n'
              +'\n'
              +'Ausgangslage / Historie mit dem Lieferanten, relevante Hintergrund-Informationen\n'
              +'Deine Verhandlungsstrategie, Dein grundsätzliches Verhandlungskonzept, Deine Ziele (minimal-maximal) in Zahlen mit Begründungen\n'
              +'Die Ziele des Lieferanten – real und/oder angenommen\n'
              +'Einwände und eine durch die Forderung ausgelöste mögliche Konfliktdynamik\n'
              +'Der Coach ermöglicht Dir, Deine Vorgehensweisen interaktiv immer weiter zu verfeinern.\n'
              +'\n'
              +'Viel Spaß in der Vorbereitung und Erfolg in der anstehenden Verhandlung!'"
            :is-user="true"
          />
        </div>

        <!-- Chat Messages -->
        <div v-for="message in messages" :key="message.id">
          <ChatBubble
            :message="message.content"
            :is-user="message.role === 'user'"
            :is-streaming="message.isStreaming"
            :source-count="message.retrievedDocs?.length || 0"
          />
        </div>
      </div>
    </div>

    <!-- Bottom Controls -->
    <div class="bottom-controls" :class="{ 'sidebar-open': appStore.isSidebarVisible }">
      <div class="controls-container">
        <UiLiquidGlassCard
          v-if="!appStore.isSidebarVisible"
          size="sm"
          rounded
          @click="appStore.toggleSidebar"
        >
          <UButton
            icon="i-lucide-menu"
            variant="link"
            active-variant="link"
            class="text-secondary-custom hover:text-white"
          />
        </UiLiquidGlassCard>

        <!--        Favorites Button below -->
        <UiLiquidGlassCard
          size="sm"
          rounded
          @click="appStore.toggleFavorites"
        >
          <UButton
            v-if="appStore.showFavorites"
            icon="i-lucide-message-circle"
            class="text-secondary-custom hover:text-white"
            variant="link"
            active-variant="link"
          />
          <div v-else class="p-2">
            <img
              src="/images/star-filled.svg"
              alt="Favorites"
              class="w-4.5 h-4.5 opacity-70 hover:opacity-100 transition-opacity cursor-pointer"
            />
          </div>
        </UiLiquidGlassCard>
        <div class="flex-1">
          <ChatInput
            :loading="loading"
            class="responsive-input"
            @submit="sendMessage"
          />
        </div>
      </div>
    </div>

    <!-- Chat Goal Modal -->
    <!--    <ChatGoalModal -->
    <!--      v-model="showGoalModal" -->
    <!--      :prevent-close="!hasCheckedSession" -->
    <!--      @session-created="handleSessionCreated" -->
    <!--    /> -->
  </div>
</template>

<style scoped>
.chat-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chat-header {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
  transition: all 0.3s ease-in-out;
}

.chat-header.sidebar-open {
  left: calc(50% + 140px);
}

.chat-main {
  flex: 1;
  padding: 6rem 2rem 8rem 2rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
}

.chat-main.sidebar-open {
  margin-left: 320px;
  width: calc(100% - 320px);
}

.bottom-controls {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 4rem);
  max-width: 800px;
  z-index: 10;
  transition: all 0.3s ease-in-out;
}

.bottom-controls.sidebar-open {
  left: calc(50% + 160px);
  width: calc(100% - 320px - 4rem);
}

.controls-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding-right: 1rem;
  margin-bottom: 2rem;
}

.welcome-container {
  margin-bottom: 2rem;
}

/* Custom scrollbar */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

@media (max-width: 1024px) {
  .chat-header.sidebar-open {
    left: 50%;
  }

  .chat-main.sidebar-open {
    margin-left: 0;
    width: 100%;
  }

  .bottom-controls.sidebar-open {
    left: 50%;
    width: calc(100% - 4rem);
  }
}

@media (max-width: 768px) {
  .chat-header {
    top: 1rem;
  }

  .chat-main {
    padding: 5rem 1rem 8rem 1rem;
  }

  .chat-messages {
    padding-right: 0.5rem;
  }

  .bottom-controls {
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    width: auto;
    transform: none;
  }

  .controls-container {
    gap: 0.5rem;
  }
}
</style>
