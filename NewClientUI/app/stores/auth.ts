import { defineStore, acceptHMRUpdate } from 'pinia'
import type { User } from '~/composables/useAuth'

export interface AuthStoreState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  token: string | null
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthStoreState => ({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    token: null
  }),

  getters: {
    userName: (state) => state.user?.name || state.user?.email?.split('@')[0] || 'User',
    userEmail: (state) => state.user?.email || '',
    userAvatar: (state) => state.user?.avatar || '',
    userInitials: (state) => {
      const name = state.user?.name || state.user?.email?.split('@')[0] || 'U'
      return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)
    },
    isLoggedIn: (state) => state.isAuthenticated && !!state.user,
    hasValidToken: (state) => !!state.token
  },

  actions: {
    setUser(user: User | null) {
      this.user = user
      this.isAuthenticated = !!user
      this.error = null
    },

    setToken(token: string | null) {
      this.token = token
      if (!token) {
        this.user = null
        this.isAuthenticated = false
      }
    },

    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    setError(error: string | null) {
      this.error = error
    },

    updateUserProfile(updates: Partial<User>) {
      if (this.user) {
        this.user = { ...this.user, ...updates }
      }
    },

    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.error = null
      this.isLoading = false
    },

    // Initialize store from localStorage or other persistent storage
    initialize(token: string | null, user: User | null) {
      this.token = token
      this.user = user
      this.isAuthenticated = !!user && !!token
    }
  },

  persist: {
    key: 'auth-store',
    storage: import.meta.client ? localStorage : undefined,
    paths: ['user', 'token', 'isAuthenticated']
  }
})

// Enable hot module replacement
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useAuthStore, import.meta.hot))
}
