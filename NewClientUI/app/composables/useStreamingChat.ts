import { AUTH_CONFIG } from '~/config/api'
import { useAuth } from '~/composables/useAuth'

export interface StreamMessage {
  content: string
  retrieved_docs?: any[]
  docs_mmr?: any[]
  query_id?: string
  session_id?: string
  is_web_searched?: boolean
}

export interface ChatStreamOptions {
  collectionId: string
  sessionId?: string
  content: string
  customPrompt?: string
  topK?: number
  searchMethod?: string
  markdownSupport?: boolean
  onChunk?: (chunk: StreamMessage) => void
  onComplete?: (fullContent: string) => void
  onError?: (error: Error) => void
}

export function useStreamingChat() {
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const currentMessage = ref('')
  const abortController = ref<AbortController | null>(null)
  const { getToken } = useAuth()

  async function sendStreamingMessage(options: ChatStreamOptions) {
    const {
      collectionId,
      sessionId,
      content,
      customPrompt,
      topK = 10,
      searchMethod,
      markdownSupport = false,
      onChunk,
      onComplete,
      onError
    } = options

    loading.value = true
    error.value = null
    currentMessage.value = ''
    
    // Create new abort controller for this request
    abortController.value = new AbortController()

    try {
      const requestBody = {
        content,
        session_id: sessionId,
        custom_prompt: customPrompt,
        top_k: topK,
        search_method: searchMethod,
        markdown_support_on: markdownSupport,
        inline_pdf_content: ''
      }

      // Get auth token
      const token = getToken()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (token) {
        headers[AUTH_CONFIG.TOKEN_HEADER] = `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`
      }

      // Use the proxied endpoint path
      const endpoint = `/api/query/collection/${collectionId}/query_stream`
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController.value.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error('No response body')
      }

      let buffer = ''
      
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        // Decode the chunk
        const chunk = decoder.decode(value, { stream: true })
        buffer += chunk

        // Process complete JSON objects from buffer
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data: StreamMessage = JSON.parse(line)
              
              // Accumulate the message content
              if (data.content) {
                currentMessage.value += data.content
              }

              // Call the chunk callback
              if (onChunk) {
                onChunk(data)
              }
            } catch (e) {
              console.error('Error parsing streaming response:', e)
            }
          }
        }
      }

      // Process any remaining buffer
      if (buffer.trim()) {
        try {
          const data: StreamMessage = JSON.parse(buffer)
          if (data.content) {
            currentMessage.value += data.content
          }
          if (onChunk) {
            onChunk(data)
          }
        } catch (e) {
          console.error('Error parsing final buffer:', e)
        }
      }

      // Call complete callback with full message
      if (onComplete) {
        onComplete(currentMessage.value)
      }
    } catch (err: any) {
      if (err.name === 'AbortError') {
        console.log('Stream aborted')
      } else {
        error.value = err
        if (onError) {
          onError(err)
        }
      }
    } finally {
      loading.value = false
      abortController.value = null
    }
  }

  function abortStream() {
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
      loading.value = false
    }
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    currentMessage: readonly(currentMessage),
    sendStreamingMessage,
    abortStream
  }
}
