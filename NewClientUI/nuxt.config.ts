// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    '@nuxt/eslint',
    '@nuxt/ui-pro',
    '@nuxtjs/mdc',
    '@nuxthub/core',
    '@pinia/nuxt',
    'nuxt-auth-utils'
  ],

  devServer: {
    port: 5179,
    host: '0.0.0.0'
  },

  devtools: {
    enabled: true
  },

  css: ['~/assets/css/main.css'],

  mdc: {
    highlight: {
      // noApiRoute: true
      shikiEngine: 'javascript'
    }
  },

  future: {
    compatibilityVersion: 4
  },

  experimental: {
    viewTransition: true
  },

  compatibilityDate: '2024-07-11',

  nitro: {
    experimental: {
      openAPI: true
    },
    devProxy: {
      '/api/query': {
        target: process.env.QUERY_BASE_URL || 'http://localhost:8003',
        changeOrigin: true,
        prependPath: true
      },
      '/api/loader': {
        target: process.env.LOADER_BASE_URL || 'http://localhost:8002',
        changeOrigin: true,
        prependPath: true
      },
      '/slash-commands': {
        target: process.env.QUERY_BASE_URL || 'http://localhost:8003',
        changeOrigin: true,
        prependPath: true
      }
    }
  },

  hub: {
    ai: true,
    database: false
  },

  vite: {
    optimizeDeps: {
      include: ['debug']
    },

    $server: {
      build: {
        rollupOptions: {
          output: {
            preserveModules: true
          }
        }
      }
    }
  },

  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  }
})
