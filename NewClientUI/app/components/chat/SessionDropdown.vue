<template>
  <div class="session-dropdown-container" @click.stop>
    <UPopover v-model:open="isOpen" :popper="{ placement: 'bottom-end' }">
      <UIcon
        name="i-lucide-more-vertical"
        class="session-dropdown-trigger"
        :class="{ 'visible': isHovered || isOpen }"
        @click.stop="isOpen = !isOpen"
      />

      <template #content>
        <div class="dropdown-menu">
          <!-- Rename -->
          <button
            class="dropdown-item"
            @click="handleRename"
          >
            <UIcon name="i-lucide-edit-2" class="w-4 h-4" />
            <span>Umbenennen</span>
          </button>

          <!-- Toggle Favorite -->
          <button
            class="dropdown-item"
            @click="handleToggleFavorite"
          >
            <UIcon 
              :name="session.is_favorite ? 'i-lucide-star-off' : 'i-lucide-star'" 
              class="w-4 h-4" 
            />
            <span>{{ session.is_favorite ? 'Favorit entfernen' : 'Als Favorit' }}</span>
          </button>

          <!-- Download -->
          <button
            class="dropdown-item"
            @click="handleDownload"
          >
            <UIcon name="i-lucide-download" class="w-4 h-4" />
            <span>Als Datei herunterladen</span>
          </button>

          <!-- Show Files (if PDFs uploaded) -->
          <button
            v-if="session.uploaded_pdfs"
            class="dropdown-item"
            @click="handleShowFiles"
          >
            <UIcon name="i-lucide-folder-open" class="w-4 h-4" />
            <span>Hochgeladene Dateien</span>
          </button>

          <div class="dropdown-divider" />

          <!-- Delete -->
          <button
            class="dropdown-item text-red-400 hover:text-red-300 hover:bg-red-500/20"
            @click="handleDelete"
          >
            <UIcon name="i-lucide-trash-2" class="w-4 h-4" />
            <span>Löschen</span>
          </button>
        </div>
      </template>
    </UPopover>
  </div>
</template>

<script setup lang="ts">
import { useCollectionsStore, type Session } from '~/stores/collections'

interface Props {
  session: Session
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
}>()

const collectionsStore = useCollectionsStore()
const toast = useToast()

// State
const isOpen = ref(false)
const isHovered = ref(false)

// Computed
const uploadedFiles = computed(() => {
  if (!props.session.uploaded_pdfs) return []
  return props.session.uploaded_pdfs.split(',').map(f => f.trim())
})

// Parent hover state
onMounted(() => {
  const parent = document.querySelector(`.session-item[data-session-id="${props.session.id}"]`)
  if (parent) {
    parent.addEventListener('mouseenter', () => { isHovered.value = true })
    parent.addEventListener('mouseleave', () => { isHovered.value = false })
  }
})

// Methods
async function handleRename() {
  const newName = prompt('Neuer Name für die Sitzung:', props.session.title || 'Unbenannte Sitzung')
  if (!newName || newName.trim() === '') return

  try {
    await collectionsStore.renameSession(props.session.id, newName.trim())
    isOpen.value = false
    toast.add({
      title: 'Erfolgreich umbenannt',
      icon: 'i-heroicons-check-circle'
    })
    emit('refresh')
  } catch (error) {
    toast.add({
      title: 'Fehler beim Umbenennen',
      description: 'Die Sitzung konnte nicht umbenannt werden.',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  }
}

async function handleToggleFavorite() {
  try {
    await collectionsStore.updateFavoriteStatus(props.session.id, !props.session.is_favorite)
    isOpen.value = false
    toast.add({
      title: props.session.is_favorite ? 'Favorit entfernt' : 'Als Favorit markiert',
      icon: 'i-heroicons-star'
    })
    emit('refresh')
  } catch (error) {
    toast.add({
      title: 'Fehler',
      description: 'Der Favoritenstatus konnte nicht geändert werden.',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  }
}

async function handleDelete() {
  // Show confirmation
  const confirmed = confirm('Möchten Sie diese Sitzung wirklich löschen?')
  if (!confirmed) return

  try {
    await collectionsStore.deleteSession(props.session.id)
    isOpen.value = false
    toast.add({
      title: 'Sitzung gelöscht',
      icon: 'i-heroicons-trash'
    })
    emit('refresh')
  } catch (error) {
    toast.add({
      title: 'Fehler beim Löschen',
      description: 'Die Sitzung konnte nicht gelöscht werden.',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  }
}

function handleDownload() {
  // TODO: Implement download functionality
  toast.add({
    title: 'Download-Funktion',
    description: 'Diese Funktion wird noch implementiert.',
    icon: 'i-heroicons-arrow-down-tray'
  })
  isOpen.value = false
}

function handleShowFiles() {
  // Show files in a simple alert for now
  const files = uploadedFiles.value.join('\n')
  alert(`Hochgeladene Dateien:\n\n${files || 'Keine Dateien hochgeladen'}`)
  isOpen.value = false
}
</script>

<style scoped>
.session-dropdown-container {
  position: relative;
}

.session-dropdown-trigger {
  width: 1rem;
  height: 1rem;
  opacity: 0;
  transition: opacity 0.2s;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
}

.session-dropdown-trigger.visible {
  opacity: 1;
}

.session-dropdown-trigger:hover {
  color: rgba(255, 255, 255, 1);
}

.dropdown-menu {
  min-width: 200px;
  padding: 0.5rem;
  background: rgba(30, 30, 30, 0.98);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  border-radius: 0.375rem;
  transition: all 0.15s;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 0;
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
