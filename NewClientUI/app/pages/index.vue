<script setup lang="ts">
const { isAuthenticated, getToken, setToken, fetchUser, login, isLoading, error } = useAuth()
const isDev = import.meta.dev

definePageMeta({
  layout: 'general',
  auth: false
})

// Handle OAuth callback and authentication
onMounted(async () => {
  // Check for access token in URL (OAuth callback)
  const route = useRoute()
  const accessToken = route.query.access_token as string

  if (accessToken) {
    console.log('Access token found in URL:', accessToken.substring(0, 20) + '...')

    // Store token in localStorage and Pinia store
    setToken(accessToken)
    // Clean URL
    window.history.replaceState({}, document.title, '/')

    // Fetch user data and redirect to landing
    try {
      // const userData = await fetchUser()
      // if (userData) {
      console.log('User authenticated, redirecting to landing...')
      await navigateTo('/landing')
      // }
    } catch (error) {
      console.error('Failed to fetch user data:', error)
    }
  } else if (isAuthenticated.value || getToken()) {
    // If already authenticated, go to landing
    await navigateTo('/landing')
  }
})

// Redirect authenticated users to landing
watchEffect(() => {
  if (isAuthenticated.value) {
    navigateTo('/landing')
  }
})

async function handleLogin() {
  try {
    await login()
  } catch (err) {
    console.error('Login error:', err)
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-4">
    <div class="mesh-box" />
    <div class="w-full max-w-md">
      <UiLiquidGlassCard>
        <div class="space-y-6">
          <div class="space-y-2 text-center">
            <h1 class="text-3xl font-bold text-white tracking-tight">
              _fbeta
            </h1>
          </div>
          <div>
            <h2 class="text-xl font-bold mb-2 text-white/80">
              Login
            </h2>
            <p class="text-sm text-white/60">
              Click below to authenticate with your account
            </p>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
            <p class="text-sm text-red-200">
              {{ error }}
            </p>
          </div>

          <!-- Login Button -->
          <div class="space-y-4">
            <UButton
              :loading="isLoading"
              :disabled="isLoading"
              block
              size="lg"
              class="btn-primary-custom text-white border-0"
              :aria-busy="isLoading ? 'true' : 'false'"
              @click="handleLogin"
            >
              <span class="inline-flex items-center gap-2">
                <span>{{ isLoading ? 'Redirecting...' : 'Login with OAuth' }}</span>
              </span>
            </UButton>
          </div>
        </div>
      </UiLiquidGlassCard>
    </div>
  </div>
</template>
