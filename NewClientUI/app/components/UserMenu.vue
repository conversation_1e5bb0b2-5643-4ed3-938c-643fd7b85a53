<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'

interface Props {
  collapsed?: boolean
}

defineProps<Props>()

const { user, logout, isLoading } = useAuth()
const appStore = useAppStore()
const authStore = useAuthStore()

// Computed properties for auth store getters
const userAvatar = computed(() => authStore.userAvatar)
const userName = computed(() => authStore.userName || 'User')
const userEmail = computed(() => authStore.userEmail || localStorage.getItem('user_email') || '')

const open = ref(false)

function handleProfileClick() {
  open.value = false
  console.log('Profile clicked')
  // TODO: Navigate to profile or open profile modal
}

function handleSettingsClick() {
  open.value = false
  console.log('Settings clicked')
  // TODO: Navigate to settings or open settings modal
}

async function handleLogoutClick() {
  open.value = false
  try {
    await logout()
    appStore.addNotification({
      type: 'success',
      title: 'Logged out successfully'
    })
  } catch (error) {
    console.error('Logout error:', error)
    appStore.addNotification({
      type: 'error',
      title: 'Logout failed',
      message: 'Please try again'
    })
  }
}
</script>

<template>
  <div>
    <!-- User Menu Button with Liquid Glass Design -->
    <UPopover v-model:open="open" class="w-12" :ui="{ content: 'p-0' }">
      <UiLiquidGlassCard
        size="sm"
        rounded
        class="cursor-pointer transition-all duration-200 hover:scale-105"
      >
        <UButton
          icon="i-lucide-user"
          variant="link"
          active-variant="link"
          class="text-secondary-custom hover:text-white"
          :loading="isLoading"
        />
      </UiLiquidGlassCard>

      <template #content>
        <div class="p-0">
          <div class="bg-gray-900/95 backdrop-blur-xl rounded-lg shadow-2xl">
            <div class="p-4 space-y-4">
              <!-- User Info Header -->
              <div class="flex items-center gap-3 pb-3 border-b border-white/20">
                <div class="w-10 h-10 rounded-full flex items-center justify-center shadow-lg">
                  <img
                    v-if="userAvatar"
                    :src="userAvatar"
                    :alt="userName"
                    class="w-full h-full rounded-full object-cover"
                  >
                  <span v-else class="text-white font-semibold text-sm">
                    {{ userName.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-semibold text-white truncate">
                    {{ userName }}
                  </p>
                  <p class="text-xs text-gray-400 truncate">
                    {{ userEmail || 'No email available' }}
                  </p>
                </div>
                <UButton
                  color="neutral"
                  variant="ghost"
                  icon="i-lucide-x"
                  size="xs"
                  class="text-gray-400 hover:text-white transition-colors"
                  @click="open = false"
                />
              </div>

              <!-- Menu Actions -->
              <div class="space-y-1">
                <button
                  class="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded-md transition-all duration-150"
                  @click="handleProfileClick"
                >
                  <UIcon name="i-lucide-user" class="w-4 h-4 mr-3" />
                  <span>Profile</span>
                </button>

                <button
                  class="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded-md transition-all duration-150"
                  @click="handleSettingsClick"
                >
                  <UIcon name="i-lucide-settings" class="w-4 h-4 mr-3" />
                  <span>Settings</span>
                </button>

                <div class="pt-2 mt-2 border-t border-white/20">
                  <button
                    class="w-full flex items-center px-3 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-md transition-all duration-150"
                    @click="handleLogoutClick"
                  >
                    <UIcon name="i-lucide-log-out" class="w-4 h-4 mr-3" />
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </UPopover>
  </div>
</template>

<style scoped>
/* Additional liquid glass effect enhancements */
:deep(.backdrop-blur-xl) {
  backdrop-filter: blur(24px) saturate(180%);
  -webkit-backdrop-filter: blur(24px) saturate(180%);
}

/* Smooth transitions for dropdown items */
:deep(.group) {
  position: relative;
  overflow: hidden;
}

:deep(.group::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

:deep(.group:hover::before) {
  left: 100%;
}
</style>
