<template>
  <div
    :class="[
      'liquid-glass-card',
      size === 'sm' ? 'p-2' : size === 'lg' ? 'p-8' : 'p-6',
      { 'liquid-glass-clear': clear }
    ]"
    :style="rounded ? { borderRadius: '16px' } : { borderRadius: '12px' }"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
  clear?: boolean
}

withDefaults(defineProps<Props>(), {
  size: 'md',
  rounded: false,
  clear: false
})
</script>

<style scoped>
.liquid-glass-card {
  /* Background: Two layered gradients as specified in Figma */
  background:
    linear-gradient(0deg, rgba(255, 255, 255, 0.01), rgba(255, 255, 255, 0.01)),
    linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.06) 100%);

  /* Backdrop filter with exact blur value from Figma */
  backdrop-filter: blur(120px);
  -webkit-backdrop-filter: blur(120px);

  /* Border with gradient image source */
  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.04) 100%);
  border-image-slice: 1;

  /* Box shadow with exact inset shadow from Figma */
  box-shadow: 0px 5px 9px 0px rgba(255, 255, 255, 0.08) inset;

  position: relative;
  overflow: hidden;
}

.liquid-glass-card > * {
  position: relative;
  z-index: 2;
}

.liquid-glass-card:hover {
  /* Enhanced hover state while maintaining Figma design */
  background:
    linear-gradient(0deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.02)),
    linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.08) 100%);

  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.06) 100%);

  box-shadow: 0px 5px 9px 0px rgba(255, 255, 255, 0.12) inset;

  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Clear variant - transparent background with visible shadows for outline */
.liquid-glass-clear {
  /* Transparent background */
  background: transparent;

  /* Keep the backdrop filter for subtle effect */
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);

  /* Keep border for outline visibility */
  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.02) 100%);
  border-image-slice: 1;

  /* Keep shadows for depth and outline */
  box-shadow:
    0px 5px 9px 0px rgba(255, 255, 255, 0.05) inset,
    0px 2px 8px 0px rgba(0, 0, 0, 0.2);
}

.liquid-glass-clear:hover {
  /* Subtle hover effect for clear variant */
  background: rgba(255, 255, 255, 0.02);

  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.04) 100%);

  box-shadow:
    0px 5px 9px 0px rgba(255, 255, 255, 0.08) inset,
    0px 2px 8px 0px rgba(0, 0, 0, 0.25);

  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
