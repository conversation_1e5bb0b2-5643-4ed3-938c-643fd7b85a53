# Use Node.js runtime for development server
FROM node:20-alpine

WORKDIR /app

# Install bash for environment variable substitution
RUN apk add --no-cache bash

# Copy package files
COPY NewClientUI/package*.json ./

# Install dependencies with npm
RUN npm install

# Copy source code
COPY NewClientUI/ ./

# Copy environment substitution script
COPY NewClientUI/replacer /replacer
RUN chmod +x /replacer

# Expose port
EXPOSE 8005

# Start the development server with environment variable substitution
CMD ["/bin/bash", "-c", "/replacer && npm run dev -- --host 0.0.0.0 --port 8005"]

#https://login.microsoftonline.com/525e9c34-ba0f-4d67-82b6-acda016765d2/oauth2/v2.0/authorize?client_id=400f1e8f-880c-4aa3-b52b-7f451383c45c&response_type=code&redirect_uri=http://localhost:8005/api/query/callback&response_mode=query&scope=openid%20profile%20email%20api://400f1e8f-880c-4aa3-b52b-7f451383c45c/user_impersonation&state=12345