<template>
  <div v-if="appStore.isSidebarVisible" class="chat-sidebar-container">
    <!-- Floating Sidebar -->
    <UiLiquidGlassCard
      class="floating-sidebar"
      size="lg"
      rounded
    >
      <!-- Header with New Chat Button -->
      <div class="sidebar-header">
        <UButton
          icon="i-lucide-plus"
          label="New chat"
          variant="outline"
          class="w-full text-white/90 hover:text-white border-white/20 hover:border-white/40"
          @click="handleNewChat"
        />
      </div>

      <!-- Search Bar (Hidden by default) -->
      <div v-if="showSearch" class="search-container">
        <UInput
          v-model="searchQuery"
          placeholder="Search..."
          icon="i-lucide-search"
          class="w-full"
          :ui="{
            base: 'bg-white/10 border-white/20',
            placeholder: 'placeholder-white/50',
            color: { white: { outline: 'text-white' } }
          }"
          @keyup.esc="closeSearch"
        />
      </div>

      <!-- Sessions List -->
      <div class="sessions-list">
        <!-- Recent/All Sessions (when not showing favorites) -->
        <div v-if="!appStore.showFavorites">
          <!-- Recents Section Header -->
          <div class="section-main-header">
            <h3 class="text-lg font-bold text-white">
              Recents
            </h3>
            <div class="header-icons">
              <UIcon
                name="i-lucide-search"
                class="w-5 h-5 text-white/80 hover:text-white cursor-pointer"
                @click="toggleSearch"
              />
              <UIcon
                name="i-lucide-plus"
                class="w-5 h-5 text-white/80 hover:text-white cursor-pointer"
                @click="handleNewChat"
              />
              <UIcon
                :name="allSectionsExpanded ? 'i-lucide-chevron-up' : 'i-lucide-chevron-down'"
                class="w-5 h-5 text-white/80 hover:text-white cursor-pointer"
                @click="toggleAllSections"
              />
            </div>
          </div>

          <!-- Show loader if no real sessions exist -->
          <div v-if="!hasAnySessions">
            <!-- Loading State -->
            <div class="loading-container">
              <div class="flex flex-col items-center justify-center py-8">
                <UIcon name="i-lucide-loader-2" class="w-8 h-8 text-white/40 animate-spin mb-3" />
                <p class="text-sm text-white/50">
                  Loading sessions...
                </p>
              </div>
            </div>
          </div>

          <!-- Real Sessions -->
          <div v-else>
            <!-- Favorites Section -->
            <div v-if="filteredFavorites.length > 0" class="session-section">
              <div class="section-header" @click="toggleSection('favorites')">
                <div class="flex items-center gap-2">
                  <UIcon
                    :name="expandedSections.favorites ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                    class="w-4 h-4 text-white/60"
                  />
                  <h3 class="text-sm font-semibold text-white/90">
                    Favoriten
                  </h3>
                </div>
                <span class="text-xs text-white/50">{{ filteredFavorites.length }}</span>
              </div>
              <div v-show="expandedSections.favorites" class="session-items">
                <div
                  v-for="session in filteredFavorites"
                  :key="session.id"
                  :data-session-id="session.id"
                  class="session-item group"
                  :class="{ active: currentSessionId === session.id }"
                  @click="selectSession(session.id)"
                >
                  <div class="session-item-content">
                    <UIcon name="i-lucide-message-circle" class="w-4 h-4 text-white/60 shrink-0" />
                    <span class="session-title">{{ session.title || 'Untitled Session' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
              </div>
            </div>

            <!-- Folders with Sessions -->
            <div v-for="(sessions, folderName) in folderSessions" :key="folderName" class="session-section">
              <div class="section-header" @click="toggleSection(folderName)">
                <div class="flex items-center gap-2">
                  <UIcon
                    :name="expandedSections[folderName] ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                    class="w-4 h-4 text-white/60"
                  />
                  <UIcon name="i-lucide-folder" class="w-4 h-4 text-white/70" />
                  <h3 class="text-sm font-semibold text-white/90">
                    {{ folderName }}
                  </h3>
                </div>
                <span class="text-xs text-white/50">{{ sessions.length }}</span>
              </div>
              <div v-show="expandedSections[folderName]" class="session-items">
                <div
                  v-for="session in sessions"
                  :key="session.id"
                  :data-session-id="session.id"
                  class="session-item group"
                  :class="{ active: currentSessionId === session.id }"
                  @click="selectSession(session.id)"
                >
                  <div class="session-item-content">
                    <UIcon name="i-lucide-message-circle" class="w-4 h-4 text-white/60 shrink-0" />
                    <span class="session-title">{{ session.title || 'Unbenannte Sitzung' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
              </div>
            </div>

            <!-- Unassigned Sessions -->
            <div v-if="unassignedSessions.length > 0" class="session-section">
              <div v-show="expandedSections.unassigned" class="session-items">
                <div
                  v-for="session in unassignedSessions"
                  :key="session.id"
                  :data-session-id="session.id"
                  class="session-item group"
                  :class="{ active: currentSessionId === session.id }"
                  @click="selectSession(session.id)"
                >
                  <div class="session-item-content">
                    <UIcon name="i-lucide-message-circle" class="w-4 h-4 text-white/60 shrink-0" />
                    <span class="session-title">{{ session.title || 'Unbenannte Sitzung' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="appStore.showFavorites">
          <!-- Favorites Section Header -->
          <div class="section-main-header">
            <h3 class="text-lg font-bold text-white">
              Favorites
            </h3>
            <div class="header-icons">
              <UIcon
                :name="expandedSections.favoritesOnly ? 'i-lucide-chevron-up' : 'i-lucide-chevron-down'"
                class="w-5 h-5 text-white/80 hover:text-white cursor-pointer"
                @click="toggleSection('favoritesOnly')"
              />
            </div>
          </div>

          <div class="session-section">
            <div v-show="expandedSections.favoritesOnly" class="session-items">
              <div
                v-for="session in filteredFavorites"
                :key="session.id"
                :data-session-id="session.id"
                class="session-item group"
                :class="{ active: currentSessionId === session.id }"
                @click="selectSession(session.id)"
              >
                <div class="session-item-content">
                  <UIcon name="i-lucide-star" class="w-4 h-4 text-yellow-400 shrink-0" />
                  <span class="session-title">{{ session.title || 'Untitled Session' }}</span>
                </div>
                <SessionDropdown :session="session" @refresh="refreshSessions" />
              </div>
              <div v-if="filteredFavorites.length === 0" class="empty-state">
                <UIcon name="i-lucide-star-off" class="w-8 h-8 text-white/30 mx-auto mb-2" />
                <p class="text-xs text-white/50 text-center">
                  No favorites yet
                </p>
              </div>
            </div>
          </div>
        </div>
      </div> <!-- Close sessions-list -->

      <!-- Control Buttons -->
      <div class="sidebar-controls">
        <UiLiquidGlassCard
          size="sm"
          class="cursor-pointer"
          rounded
          @click="appStore.toggleSidebar"
          clear
        >
          <UButton
            icon="i-lucide-x"
            variant="link"
            active-variant="link"
            size="lg"
            class="cursor-pointer control-btn text-white hover:text-white"
          />
        </UiLiquidGlassCard>
      </div>
    </UiLiquidGlassCard>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useCollectionsStore } from '~/stores/collections'
import SessionDropdown from './SessionDropdown.vue'

const appStore = useAppStore()
const collectionsStore = useCollectionsStore()
const router = useRouter()

// State
const searchQuery = ref('')
const showSearch = ref(false)
const expandedSections = ref<Record<string, boolean>>({
  'favorites': true,
  'unassigned': true,
  'demo-folder': true,
  'favoritesOnly': true
})

// Computed properties from store
const favoriteSessions = computed(() => collectionsStore.favoriteSessions)
const unassignedSessions = computed(() => collectionsStore.unassignedSessions)
const folderSessions = computed(() => collectionsStore.folderSessions)
const currentSessionId = computed(() => collectionsStore.currentSession?.id)

// Check if there are any real sessions
const hasAnySessions = computed(() => {
  return favoriteSessions.value.length > 0
    || unassignedSessions.value.length > 0
    || Object.keys(folderSessions.value).length > 0
})

// Filtered sessions based on search
const filteredFavorites = computed(() => {
  if (!searchQuery.value) return favoriteSessions.value
  return favoriteSessions.value.filter(s =>
    s.title?.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// Initialize expanded sections for folders
watchEffect(() => {
  Object.keys(folderSessions.value).forEach((folder) => {
    if (!(folder in expandedSections.value)) {
      expandedSections.value[folder] = false
    }
  })
})

// Methods
function toggleSection(section: string) {
  expandedSections.value[section] = !expandedSections.value[section]
}

async function selectSession(sessionId: string) {
  await collectionsStore.selectSession(sessionId)
  // Close sidebar on mobile after selection
  if (window.innerWidth < 768) {
    appStore.toggleSidebar()
  }
}

function handleNewChat() {
  // Emit event to parent to show goal modal
  const event = new CustomEvent('show-goal-modal')
  window.dispatchEvent(event)
}

function handleCreateFolder() {
  // Show create folder modal
  const event = new CustomEvent('show-create-folder-modal')
  window.dispatchEvent(event)
}

async function refreshSessions() {
  if (collectionsStore.currentCollection) {
    await collectionsStore.fetchSessions(collectionsStore.currentCollection.id)
  }
}

function toggleSearch() {
  showSearch.value = !showSearch.value
  if (!showSearch.value) {
    searchQuery.value = ''
  }
}

function closeSearch() {
  showSearch.value = false
  searchQuery.value = ''
}

// Computed property to check if all sections are expanded
const allSectionsExpanded = computed(() => {
  // Only consider relevant sections based on current view
  if (appStore.showFavorites) {
    return expandedSections.value.favoritesOnly
  } else {
    const relevantSections = Object.entries(expandedSections.value)
      .filter(([key]) => key !== 'favoritesOnly')
    return relevantSections.every(([_, value]) => value)
  }
})

function toggleAllSections() {
  const targetState = !allSectionsExpanded.value

  if (appStore.showFavorites) {
    expandedSections.value.favoritesOnly = targetState
  } else {
    Object.keys(expandedSections.value).forEach((key) => {
      if (key !== 'favoritesOnly') {
        expandedSections.value[key] = targetState
      }
    })
  }
}
</script>

<style scoped>
.chat-sidebar-container {
  position: relative;
  z-index: 10;
}

.floating-sidebar {
  position: fixed;
  left: 2rem;
  top: 2rem;
  bottom: 2rem;
  width: 320px;
  transition: all 0.3s ease-in-out;
  z-index: 15;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 1.5rem;
}

.sidebar-header {
  margin-bottom: 1rem;
}

.search-container {
  margin-bottom: 1.5rem;
}

.sessions-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 0.5rem;
  margin-right: -0.5rem;
}

/* Custom scrollbar */
.sessions-list::-webkit-scrollbar {
  width: 6px;
}

.sessions-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.sessions-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.sessions-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.session-section {
  margin-bottom: 1rem;
}

.section-main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
}

.header-icons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
  user-select: none;
}

.section-header:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.session-items {
  margin-bottom: 0.5rem;
}

.session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.125rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.session-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.session-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

.session-item-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.session-title {
  flex: 1;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  truncate: true;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.empty-state {
  padding: 2rem 1rem;
  text-align: center;
}

.loading-container {
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-controls {
  position: absolute;
  bottom: 0rem;
  right: 0rem;
  display: flex;
  gap: 0.5rem;
}

.clickable-card {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-card:hover {
  transform: scale(1.05);
}

.delete-card {
  padding: 0.25rem;
  min-width: auto;
}

.show-sidebar-btn {
  position: fixed;
  top: 2rem;
  left: 2rem;
  z-index: 50;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.show-sidebar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .floating-sidebar {
    left: 1rem;
    width: 250px;
  }

  .show-sidebar-btn {
    left: 1rem;
  }
}
</style>
