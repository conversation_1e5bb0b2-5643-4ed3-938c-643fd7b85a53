import { useAuthFetch } from '~/composables/useAuthFetch'

export interface Collection {
  id: string
  name: string
  description: string
  iconKey: string
  sessions?: any[]
  documents?: any[]
}

export interface CollectionsResponse {
  collections: Omit<Collection, 'iconKey'>[]
  user_info: {
    user_name: string
    email: string
  }
}

export function useDomainList() {
  const collections = ref<Collection[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const userInfo = ref<{ user_name: string; email: string } | null>(null)

  // Available icon keys for random assignment
  const iconKeys = ['01', '02', '03', '04']

  // Function to get a random icon key
  const getRandomIconKey = () => {
    return iconKeys[Math.floor(Math.random() * iconKeys.length)]
  }

  // Fetch collections from API
  const fetchCollections = async () => {
    isLoading.value = true
    error.value = null

    try {
      const { get } = useAuthFetch()
      const data = await get<CollectionsResponse>('/api/loader/collections')
      
      if (data && data.collections) {
        // Add random icon keys to each collection
        collections.value = data.collections.map(collection => ({
          ...collection,
          iconKey: getRandomIconKey()
        }))
        
        // Store user info
        if (data.user_info) {
          userInfo.value = data.user_info
        }
      } else {
        collections.value = []
      }
    } catch (err) {
      console.error('Error fetching collections:', err)
      error.value = err instanceof Error ? err.message : 'Failed to fetch collections'
      collections.value = []
    } finally {
      isLoading.value = false
    }
  }

  // Auto-fetch on composable use
  onMounted(() => {
    fetchCollections()
  })

  return {
    collections: readonly(collections),
    isLoading: readonly(isLoading),
    error: readonly(error),
    userInfo: readonly(userInfo),
    fetchCollections
  }
}
