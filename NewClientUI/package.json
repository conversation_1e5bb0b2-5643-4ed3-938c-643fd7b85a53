{"name": "client-ui", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@ai-sdk/vue": "^1.2.12", "@iconify-json/logos": "^1.2.4", "@iconify-json/lucide": "^1.2.53", "@iconify-json/simple-icons": "^1.2.40", "@nuxt/ui-pro": "^3.2.0", "@nuxthub/core": "^0.9.0", "@nuxtjs/mdc": "^0.17.0", "@pinia/nuxt": "^0.11.2", "@types/marked": "^5.0.2", "@vueuse/core": "^13.6.0", "ai": "^4.3.16", "date-fns": "^4.1.0", "marked": "^16.1.2", "nuxt": "^3.17.6", "nuxt-auth-utils": "^0.5.20", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "shiki-stream": "^0.1.2"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "@types/node": "^22.15.34", "eslint": "^9.30.0", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"ignoredBuiltDependencies": ["workerd"]}, "packageManager": "pnpm@10.12.4", "overrides": {"vue": "latest"}}