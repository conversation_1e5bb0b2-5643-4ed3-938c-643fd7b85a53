// Browser-compatible UUID generator
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

export interface DummyChat {
  id: string
  title: string
  userId: string
  createdAt: Date
  messages: DummyMessage[]
}

export interface DummyMessage {
  id: string
  chatId: string
  role: 'user' | 'assistant'
  content: string
  createdAt: Date
}

export interface DummyUser {
  id: string
  email: string
  name: string
  avatar: string
  username: string
  provider: 'github',
  providerId: number
  createdAt: Date
}

const dummyUser: DummyUser = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Demo User',
  avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
  username: 'demouser',
  provider: 'github',
  providerId: 1,
  createdAt: new Date('2024-01-01T00:00:00Z')
}

const dummyChats: DummyChat[] = [
  {
    id: 'chat-1',
    title: 'Getting Started with Vue 3',
    userId: 'user-1',
    createdAt: new Date('2024-01-15T10:00:00Z'),
    messages: [
      {
        id: 'msg-1',
        chatId: 'chat-1',
        role: 'user',
        content: 'Can you explain the basics of Vue 3?',
        createdAt: new Date('2024-01-15T10:00:00Z')
      },
      {
        id: 'msg-2',
        chatId: 'chat-1',
        role: 'assistant',
        content: 'Vue 3 is a progressive JavaScript framework for building user interfaces. It features improved performance, better TypeScript support, and the Composition API which provides better logic reuse and organization.',
        createdAt: new Date('2024-01-15T10:00:30Z')
      }
    ]
  },
  {
    id: 'chat-2',
    title: 'Nuxt.js Best Practices',
    userId: 'user-1',
    createdAt: new Date('2024-01-14T15:30:00Z'),
    messages: [
      {
        id: 'msg-3',
        chatId: 'chat-2',
        role: 'user',
        content: 'What are some Nuxt.js best practices?',
        createdAt: new Date('2024-01-14T15:30:00Z')
      },
      {
        id: 'msg-4',
        chatId: 'chat-2',
        role: 'assistant',
        content: 'Here are key Nuxt.js best practices:\n\n1. **Use server-side rendering (SSR)** for better SEO\n2. **Leverage composables** for reusable logic\n3. **Optimize images** with `<NuxtImg>`\n4. **Use middleware** for authentication and route protection\n5. **Implement proper error handling** with error.vue\n6. **Utilize auto-imports** for cleaner code',
        createdAt: new Date('2024-01-14T15:30:30Z')
      }
    ]
  },
  {
    id: 'chat-3',
    title: 'TypeScript Tips',
    userId: 'user-1',
    createdAt: new Date('2024-01-13T09:15:00Z'),
    messages: [
      {
        id: 'msg-5',
        chatId: 'chat-3',
        role: 'user',
        content: 'Give me some TypeScript tips for Vue projects',
        createdAt: new Date('2024-01-13T09:15:00Z')
      },
      {
        id: 'msg-6',
        chatId: 'chat-3',
        role: 'assistant',
        content: 'Here are essential TypeScript tips for Vue:\n\n- Use `defineComponent` for better type inference\n- Leverage `ref<T>()` and `reactive<T>()` with proper typing\n- Create interfaces for your data structures\n- Use `PropType` for complex prop types\n- Enable strict mode in tsconfig.json\n- Utilize Vue 3\'s built-in TypeScript support',
        createdAt: new Date('2024-01-13T09:15:30Z')
      }
    ]
  }
]

export function useDummyData() {
  const chats = ref<DummyChat[]>([...dummyChats])
  const currentUser = ref<DummyUser>(dummyUser)

  function _getChats() {
    return chats.value.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  function _getChatById(id: string) {
    return chats.value.find(chat => chat.id === id)
  }

  function _createChat(input: string) {
    const chatId = generateUUID()
    const messageId = generateUUID()

    const newChat: DummyChat = {
      id: chatId,
      title: input.slice(0, 50) + (input.length > 50 ? '...' : ''),
      userId: currentUser.value.id,
      createdAt: new Date(),
      messages: [
        {
          id: messageId,
          chatId: chatId,
          role: 'user',
          content: input,
          createdAt: new Date()
        }
      ]
    }

    chats.value.unshift(newChat)
    return newChat
  }

  function addMessage(chatId: string, content: string, role: 'user' | 'assistant') {
    const chat = chats.value.find(c => c.id === chatId)
    if (!chat) return null

    const message: DummyMessage = {
      id: generateUUID(),
      chatId: chatId,
      role: role,
      content: content,
      createdAt: new Date()
    }

    chat.messages.push(message)
    return message
  }

  function _deleteChat(id: string) {
    const index = chats.value.findIndex(chat => chat.id === id)
    if (index > -1) {
      chats.value.splice(index, 1)
      return true
    }
    return false
  }

  async function getChats() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100))
    return _getChats().map(chat => ({
      id: chat.id,
      label: chat.title,
      icon: 'i-lucide-message-circle',
      createdAt: chat.createdAt.toISOString(),
      isFavorite: true
    }))
  }

  async function getChatById(id: string) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 150))
    const chat = _getChatById(id)
    if (!chat) return null

    return {
      id: chat.id,
      title: chat.title,
      messages: chat.messages.map(msg => ({
        id: msg.id,
        content: msg.content,
        role: msg.role,
        createdAt: msg.createdAt.toISOString()
      }))
    }
  }

  async function createChat(input: string) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200))
    const chat = _createChat(input)
    return {
      id: chat.id,
      title: chat.title
    }
  }

  async function deleteChat(id: string) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100))
    return _deleteChat(id)
  }

  async function chatStream(
    chatId: string,
    messages: any[],
    onChunk: (chunk: string) => void,
    onComplete: () => void
  ) {
    // Get the last user message
    const lastMessage = messages[messages.length - 1]
    if (!lastMessage || lastMessage.role !== 'user') {
      onComplete()
      return
    }

    // Add user message to dummy data
    addMessage(chatId, lastMessage.content, 'user')

    // Simulate AI response
    let assistantResponse = ''
    await simulateStreamingResponse(
      lastMessage.content,
      (chunk: string) => {
        assistantResponse += chunk
        onChunk(chunk)
      },
      () => {
        // Add assistant message to dummy data
        addMessage(chatId, assistantResponse, 'assistant')
        onComplete()
      }
    )
  }

  return {
    chats: readonly(chats),
    currentUser: readonly(currentUser),
    getChats,
    getChatById,
    createChat,
    addMessage,
    deleteChat,
    chatStream
  }
}