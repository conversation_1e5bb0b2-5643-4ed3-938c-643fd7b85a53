<template>
  <div class="chat-bubble-container" :class="{ 'user-message': isUser, 'assistant-message': !isUser }">
    <UiLiquidGlassCard
      v-if="isUser"
      class="chat-bubble"
      :class="{
        'user-bubble': isUser
      }"
      size="sm"
    >
      <div class="message-content">
        <p class="text-white whitespace-pre-wrap message-text">
          {{ message }}
        </p>
      </div>
    </UiLiquidGlassCard>

    <div
      v-else
      class="chat-bubble assistant-bubble"
    >
      <div class="message-content">
        <div class="message-header">
          <img src="/images/sparkle.svg" alt="sparkle" class="sparkle-icon">
          <div class="message-text">
            <!-- Show message or streaming indicator -->
            <div v-if="message" v-html="formattedMessage" />
            <div v-else-if="isStreaming" class="streaming-indicator">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action buttons for assistant messages -->
      <div v-if="!isStreaming && message" class="message-actions">
        <UButton
          icon="i-lucide-volume-2"
          variant="ghost"
          size="sm"
          class="text-white"
        />
        <UButton
          icon="i-lucide-copy"
          variant="ghost"
          size="xs"
          class="text-white"
          @click="copyMessage"
        />
        <UButton
          icon="i-lucide-sliders-vertical"
          variant="ghost"
          size="xs"
          class="text-white"
        />
        <UButton
          icon="i-lucide-star"
          variant="ghost"
          size="xs"
          class="text-white"
        />
        <UButton
          v-if="sourceCount && sourceCount > 0"
          icon="i-lucide-search"
          variant="ghost"
          size="xs"
          class="text-white cursor-pointer"
        >
          {{ sourceCount }} sources
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { marked } from 'marked'

interface Props {
  message: string
  isUser: boolean
  isStreaming?: boolean
  sourceCount?: number
}

const props = defineProps<Props>()

// Convert markdown to HTML for assistant messages
const formattedMessage = computed(() => {
  if (props.isUser || !props.message) {
    return props.message
  }
  
  try {
    // Configure marked for safety and proper rendering
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: false,
      mangle: false
    })
    
    return marked(props.message)
  } catch (error) {
    console.error('Error parsing markdown:', error)
    return props.message
  }
})

// Copy message to clipboard
function copyMessage() {
  if (navigator.clipboard && props.message) {
    navigator.clipboard.writeText(props.message)
      .then(() => {
        console.log('Message copied to clipboard')
      })
      .catch(err => {
        console.error('Failed to copy:', err)
      })
  }
}
</script>

<style scoped>
.chat-bubble-container {
  display: flex;
  width: 100%;
  margin-bottom: 1rem;
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

.chat-bubble {
  max-width: 70%;
  min-width: 200px;
}

.assistant-bubble {
  padding: 1rem;
}

.message-content {
  margin-bottom: 0.75rem;
}

.message-header {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.sparkle-icon {
  color: #AD3A4F;
  font-size: 1rem;
  line-height: 1.5;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.message-text {
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  line-height: 24px;
  color: white;
  white-space: pre-wrap;
  margin-left: 20px;
  flex: 1;
  font-family: 'Lato';
}

.message-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10%;
  padding-top: 0.5rem;
  flex-wrap: wrap;
}

/* Streaming indicator animation */
.streaming-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.streaming-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  animation: pulse 1.4s infinite ease-in-out;
}

.streaming-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.streaming-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.streaming-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

/* Markdown content styling */
.message-text :deep(p) {
  margin-bottom: 0.5rem;
}

.message-text :deep(pre) {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
}

.message-text :deep(code) {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
  font-size: 0.875em;
}

.message-text :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.message-text :deep(li) {
  margin-bottom: 0.25rem;
}

@media (max-width: 768px) {
  .chat-bubble {
    max-width: 85%;
  }

  .message-actions {
    gap: 0.25rem;
  }
}
</style>
