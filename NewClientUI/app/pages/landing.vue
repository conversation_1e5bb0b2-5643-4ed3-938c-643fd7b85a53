<template>
  <div class="min-h-screen flex flex-col px-4 relative pt-[15vh]">
    <div class="mesh-box" />

    <div class="absolute top-6 right-8 z-20 flex items-center gap-2">
      <UButton
        icon="i-lucide-help-circle"
        variant="ghost"
        size="sm"
        class="text-white/90 hover:text-white"
      >
        Help & Documentation
      </UButton>
      <UserMenu />
    </div>

    <div class="w-full max-w-6xl relative z-10 mx-auto">
      <div class="text-center mb-12">
        <img src="/images/fbeta.png" alt="fbeta" class="md:h-10 mx-auto mb-8">
        <p class="text-xl font-bold text-white/80 mb-12">
          Choose specific domain and start chatting
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center">
        <UiLiquidGlassCard class="p-8">
          <div class="flex flex-col items-center space-y-4">
            <UIcon name="i-lucide-loader-2" class="w-8 h-8 text-white/60 animate-spin" />
            <p class="text-white/60">
              Loading collections...
            </p>
          </div>
        </UiLiquidGlassCard>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex justify-center">
        <UiLiquidGlassCard class="p-8 max-w-md">
          <div class="flex flex-col items-center space-y-4 text-center">
            <UIcon name="i-lucide-alert-circle" class="w-8 h-8 text-red-400" />
            <p class="text-white/80">
              Failed to load collections
            </p>
            <p class="text-sm text-white/60">
              {{ error }}
            </p>
            <UButton
              variant="ghost"
              class="text-white/60 hover:text-white"
              @click="() => window.location.reload()"
            >
              Refresh Page
            </UButton>
          </div>
        </UiLiquidGlassCard>
      </div>

      <!-- Empty State -->
      <div v-else-if="!collections || collections.length === 0" class="flex justify-center">
        <UiLiquidGlassCard class="p-8 max-w-md">
          <div class="flex flex-col items-center space-y-4 text-center">
            <UIcon name="i-lucide-inbox" class="w-8 h-8 text-white/60" />
            <p class="text-white/80">
              No collections available
            </p>
            <p class="text-sm text-white/60">
              Please contact support if you believe this is an error.
            </p>
          </div>
        </UiLiquidGlassCard>
      </div>

      <!-- Collections Grid -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div
          v-for="collection in collections"
          :key="collection.id"
          class="cursor-pointer transform transition-all duration-300 hover:scale-105"
          @click="selectDomain(collection)"
        >
          <UiLiquidGlassCard class="h-full">
            <div class="flex flex-col items-center text-center space-y-4 p-4">
              <div>
                <img
                  :src="getDomainIconPath(collection.iconKey)"
                  :alt="collection.name"
                  width="80px"
                />
              </div>

              <div class="space-y-2">
                <h3 class="text-xl font-semibold text-white">
                  {{ collection.name }}
                </h3>
                <p class="text-sm text-white/60 line-clamp-3" :title="collection.description">
                  {{ collection.description || 'No description available' }}
                </p>
              </div>
            </div>
          </UiLiquidGlassCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDomainList, type Collection } from '~/composables/useDomainList'
import { useAuthStore } from '~/stores/auth'
import { useCollectionsStore } from '~/stores/collections'

const { collections, isLoading, error, userInfo } = useDomainList()
const authStore = useAuthStore()
const collectionsStore = useCollectionsStore()
const router = useRouter()

// Update auth store with user info when available
watchEffect(() => {
  if (userInfo.value && userInfo.value.user_name) {
    authStore.updateUserProfile({
      name: userInfo.value.user_name,
      email: userInfo.value.email
    })
  }
})

function selectDomain(domain: Collection) {
  // Set the selected collection in the store
  collectionsStore.selectCollection(domain)
  
  // Store selected collection in sessionStorage for persistence
  sessionStorage.setItem('selectedCollection', JSON.stringify(domain))
  
  // Navigate to chat page
  router.push('/chat')
}

// Available domain icons
const domainIcons = [
  'domain-icon-01.png',
  'domain-icon-02.png',
  'domain-icon-03.png',
  'domain-icon-04.png'
]

// Function to get a random domain icon path
function getDomainIconPath(iconKey: string): string {
  // Use iconKey to get a consistent icon for each collection
  // but if iconKey is not valid, pick a random one
  const iconNumber = parseInt(iconKey)
  
  if (!isNaN(iconNumber) && iconNumber >= 1 && iconNumber <= 4) {
    return `/images/domainIcons/domain-icon-${iconKey.padStart(2, '0')}.png`
  }
  
  // Otherwise, pick a random icon
  const randomIndex = Math.floor(Math.random() * domainIcons.length)
  return `/images/domainIcons/${domainIcons[randomIndex]}`
}

definePageMeta({
  layout: 'general'
})
</script>
