@import "tailwindcss" theme(static);
@import "@nuxt/ui-pro";

@theme static {
  --font-sans: '<PERSON>o', sans-serif;

}

:root {
  --ui-container: var(--container-3xl);

  --color-primary: #ffffff;
  --color-primary-foreground: #0f172a;

  --color-hr: #ef4444; /* Red/Pink */
  --color-legal: #3b82f6; /* Blue */
  --color-compliance: #10b981; /* Green/Teal */
  --color-esg: #9333ea; /* Purple */
  
  /* Dark theme backgrounds */
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-surface-elevated: #334155;
  
  /* Text colors */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #ad3a4f;
  --color-text-muted: #94a3b8;

  /*Button colors */
  --color-button-primary: #2c6a80;
  --color-button-primary-foreground: #ffffff;
  --color-button-secondary: #f8fafc;
  --color-button-secondary-foreground: #0f172a;

  /* Glass effect */
  --glass-bg: rgba(51, 65, 85, 0.8);
  --glass-border: rgba(148, 163, 184, 0.2);
}

/* Utility classes for theme colors */
.text-primary-custom { color: var(--color-text-primary); }
.text-secondary-custom { color: var(--color-text-secondary); }
.text-muted-custom { color: var(--color-text-muted); }

.bg-surface { background-color: var(--color-surface); }
.bg-surface-elevated { background-color: var(--color-surface-elevated); }
.bg-background { background-color: var(--color-background); }

/* Accent color utilities */
.text-hr { color: var(--color-hr); }
.text-legal { color: var(--color-legal); }
.text-compliance { color: var(--color-compliance); }
.text-esg { color: var(--color-esg); }

/* Background color utilities */
.btn-primary-custom {
  background-color: var(--color-button-primary);
  color: var(--color-button-primary-foreground);
}
.btn-secondary-custom {
  background-color: var(--color-button-secondary);
  color: var(--color-button-secondary-foreground);
}

.bg-hr { background-color: var(--color-hr); }
.bg-legal { background-color: var(--color-legal); }
.bg-compliance { background-color: var(--color-compliance); }
.bg-esg { background-color: var(--color-esg); }

/* Glass effect utility */
.glass {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.mesh-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  z-index: 0;
  opacity: 0.4;
  background-image:
          linear-gradient(rgba(95, 91, 91, 0.3) 1px, transparent 1px),
          linear-gradient(90deg, rgba(95, 91, 91, 0.3) 1px, transparent 1px);
  background-size: 70px 70px;
}