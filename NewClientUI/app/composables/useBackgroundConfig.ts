export interface BackgroundConfig {
  variant: 'default' | 'dark' | 'light'
  animated: boolean
  showMeshBox: boolean
  meshBoxPosition: 'left' | 'right' | 'center'
  meshBoxOpacity: number
}

export function useBackgroundConfig() {
  // Default configuration for the mesh background
  const defaultConfig: BackgroundConfig = {
    variant: 'default',
    animated: false,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.4
  }

  // Configuration for pages that need the mesh box (like landing)
  const landingConfig: BackgroundConfig = {
    variant: 'default',
    animated: true,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.4
  }

  // Configuration for login and auth pages
  const authConfig: BackgroundConfig = {
    variant: 'default',
    animated: true,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.4
  }

  // Configuration for chat and main app areas
  const chatConfig: BackgroundConfig = {
    variant: 'default',
    animated: false,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.3
  }

  return {
    defaultConfig,
    landingConfig,
    authConfig,
    chatConfig
  }
}
