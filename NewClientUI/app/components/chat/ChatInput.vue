<template>
  <div class="chat-input-container">
    <UiLiquidGlassCard class="chat-input-card" size="sm">
      <form class="chat-form" @submit.prevent="handleSubmit">
        <div class="input-row">
          <!-- Legal button -->
          <UButton
            label="Legal"
            variant="soft"
            size="sm"
            class="domain-tag"
          />

          <UButton
            type="submit"
            icon="i-lucide-paperclip"
            variant="ghost"
            class="send-btn text-secondary-custom"
            :loading="loading"
            :disabled="!inputValue.trim()"
          />

          <!-- Input field -->
          <input
            v-model="inputValue"
            type="text"
            placeholder="Ask a question..."
            class="chat-input"
            :disabled="loading"
          >

          <!-- Send button -->
          <UButton
            type="submit"
            icon="i-lucide-mic"
            variant="ghost"
            class="send-btn text-secondary-custom"
            :loading="loading"
            :disabled="!inputValue.trim()"
          />
        </div>
      </form>
    </UiLiquidGlassCard>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'submit', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const inputValue = ref('')

function handleSubmit() {
  if (!inputValue.value.trim() || props.loading) return

  emit('submit', inputValue.value.trim())
  inputValue.value = ''
}
</script>

<style scoped>
.chat-input-container {
  width: 100%;
  z-index: 10;
}

.responsive-input {
  width: 100% !important;
  position: static !important;
  transform: none !important;
  left: auto !important;
  bottom: auto !important;
}

.chat-input-card {
  width: 100%;
}

.chat-form {
  width: 100%;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.chat-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: white;
  font-size: 0.875rem;
  placeholder-color: rgba(255, 255, 255, 0.5);
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-btn {
  flex-shrink: 0;
  background: rgba(59, 130, 246, 0.15) !important;
  color: rgba(59, 130, 246, 1) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

.send-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.25) !important;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .input-row {
    gap: 0.5rem;
  }
}

/* Ensure input works on mobile */
@media (max-width: 480px) {
  .domain-tag {
    display: none;
  }
}
</style>
